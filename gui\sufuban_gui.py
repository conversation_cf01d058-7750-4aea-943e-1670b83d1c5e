"""
苏服办爬虫系统主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import logging
from datetime import datetime
try:
    from crawler.sufuban_crawler import SufubanAppCrawler
except ImportError as e:
    print(f"导入爬虫模块失败: {e}")
    SufubanAppCrawler = None
from config.settings import GUI_CONFIG


class SufubanGUI:
    """苏服办GUI主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        if SufubanAppCrawler:
            self.crawler = SufubanAppCrawler(headless=True)
        else:
            self.crawler = None
        self.logger = logging.getLogger(__name__)
        self.current_user = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 窗口配置
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.resizable(True, True)
        
        # 创建主框架
        self.create_main_frame()
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 启动爬虫
        if self.crawler:
            self.crawler.start()
    
    def create_main_frame(self):
        """创建主框架"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 登录页面
        self.create_login_tab()
        
        # 数据选择页面
        self.create_data_selection_tab()
        
        # 数据展示页面
        self.create_data_display_tab()
        
        # 数据录入页面
        self.create_data_entry_tab()
        
        # 历史记录页面
        self.create_history_tab()
    
    def create_login_tab(self):
        """创建登录标签页"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="登录")
        
        # 登录表单
        form_frame = ttk.LabelFrame(login_frame, text="苏服办登录", padding=20)
        form_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
        
        # 用户名
        ttk.Label(form_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(form_frame, textvariable=self.username_var, width=30)
        self.username_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # 密码
        ttk.Label(form_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(form_frame, textvariable=self.password_var, show="*", width=30)
        self.password_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # 登录按钮
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        self.login_btn = ttk.Button(button_frame, text="登录", command=self.login_action)
        self.login_btn.pack(side=tk.LEFT, padx=5)
        
        self.logout_btn = ttk.Button(button_frame, text="注销", command=self.logout_action, state=tk.DISABLED)
        self.logout_btn.pack(side=tk.LEFT, padx=5)
        
        # 登录状态显示
        self.login_status_var = tk.StringVar(value="未登录")
        status_label = ttk.Label(form_frame, textvariable=self.login_status_var, foreground="red")
        status_label.grid(row=3, column=0, columnspan=2, pady=10)
        
        # 绑定回车键
        self.password_entry.bind('<Return>', lambda e: self.login_action())
    
    def create_data_selection_tab(self):
        """创建数据选择标签页"""
        selection_frame = ttk.Frame(self.notebook)
        self.notebook.add(selection_frame, text="数据选择")
        
        # 选择框架
        select_frame = ttk.LabelFrame(selection_frame, text="选择要爬取的数据", padding=20)
        select_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 数据类型选择
        self.data_types = {
            'personal_info': tk.BooleanVar(value=True),
            'applications': tk.BooleanVar(value=True),
            'certificates': tk.BooleanVar(value=True)
        }
        
        ttk.Checkbutton(select_frame, text="个人信息", variable=self.data_types['personal_info']).pack(anchor=tk.W, pady=5)
        ttk.Checkbutton(select_frame, text="办事进度", variable=self.data_types['applications']).pack(anchor=tk.W, pady=5)
        ttk.Checkbutton(select_frame, text="证书信息", variable=self.data_types['certificates']).pack(anchor=tk.W, pady=5)
        
        # 操作按钮
        btn_frame = ttk.Frame(select_frame)
        btn_frame.pack(pady=20)
        
        ttk.Button(btn_frame, text="开始爬取", command=self.start_crawling).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="查看缓存数据", command=self.load_cached_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="导出数据", command=self.export_data).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(select_frame, textvariable=self.progress_var).pack(pady=10)
        
        self.progress_bar = ttk.Progressbar(select_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
    
    def create_data_display_tab(self):
        """创建数据展示标签页"""
        display_frame = ttk.Frame(self.notebook)
        self.notebook.add(display_frame, text="数据展示")
        
        # 创建子标签页
        data_notebook = ttk.Notebook(display_frame)
        data_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 个人信息展示
        self.create_personal_info_display(data_notebook)
        
        # 办事进度展示
        self.create_applications_display(data_notebook)
        
        # 证书信息展示
        self.create_certificates_display(data_notebook)
    
    def create_personal_info_display(self, parent):
        """创建个人信息展示"""
        info_frame = ttk.Frame(parent)
        parent.add(info_frame, text="个人信息")
        
        # 创建表格
        columns = ('字段', '值')
        self.personal_tree = ttk.Treeview(info_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.personal_tree.heading(col, text=col)
            self.personal_tree.column(col, width=200)
        
        # 滚动条
        personal_scroll = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.personal_tree.yview)
        self.personal_tree.configure(yscrollcommand=personal_scroll.set)
        
        self.personal_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        personal_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_applications_display(self, parent):
        """创建办事进度展示"""
        apps_frame = ttk.Frame(parent)
        parent.add(apps_frame, text="办事进度")
        
        # 创建表格
        columns = ('序号', '事项名称', '状态', '申请时间', '更新时间')
        self.apps_tree = ttk.Treeview(apps_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.apps_tree.heading(col, text=col)
            self.apps_tree.column(col, width=150)
        
        # 滚动条
        apps_scroll = ttk.Scrollbar(apps_frame, orient=tk.VERTICAL, command=self.apps_tree.yview)
        self.apps_tree.configure(yscrollcommand=apps_scroll.set)
        
        self.apps_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        apps_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_certificates_display(self, parent):
        """创建证书信息展示"""
        certs_frame = ttk.Frame(parent)
        parent.add(certs_frame, text="证书信息")
        
        # 创建表格
        columns = ('证书名称', '证书编号', '发证日期', '状态')
        self.certs_tree = ttk.Treeview(certs_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.certs_tree.heading(col, text=col)
            self.certs_tree.column(col, width=180)
        
        # 滚动条
        certs_scroll = ttk.Scrollbar(certs_frame, orient=tk.VERTICAL, command=self.certs_tree.yview)
        self.certs_tree.configure(yscrollcommand=certs_scroll.set)
        
        self.certs_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        certs_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_data_entry_tab(self):
        """创建数据录入标签页"""
        entry_frame = ttk.Frame(self.notebook)
        self.notebook.add(entry_frame, text="数据录入")
        
        # 录入表单
        form_frame = ttk.LabelFrame(entry_frame, text="手动录入/编辑数据", padding=20)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 个人信息录入
        info_frame = ttk.LabelFrame(form_frame, text="个人信息", padding=10)
        info_frame.pack(fill=tk.X, pady=10)
        
        # 创建录入字段
        self.entry_vars = {}
        fields = [
            ('姓名', 'name'),
            ('身份证号', 'id_card'),
            ('手机号', 'phone'),
            ('邮箱', 'email'),
            ('地址', 'address'),
            ('性别', 'gender'),
            ('民族', 'nationality')
        ]
        
        for i, (label, key) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(info_frame, text=f"{label}:").grid(row=row, column=col, sticky=tk.W, padx=5, pady=5)
            self.entry_vars[key] = tk.StringVar()
            ttk.Entry(info_frame, textvariable=self.entry_vars[key], width=25).grid(row=row, column=col+1, padx=5, pady=5)
        
        # 操作按钮
        btn_frame = ttk.Frame(form_frame)
        btn_frame.pack(pady=20)
        
        ttk.Button(btn_frame, text="保存数据", command=self.save_manual_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空表单", command=self.clear_form).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="加载当前数据", command=self.load_current_data).pack(side=tk.LEFT, padx=5)
    
    def create_history_tab(self):
        """创建历史记录标签页"""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="历史记录")
        
        # 创建表格
        columns = ('时间', '操作', '状态', '消息', '数据量')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=150)
        
        # 滚动条
        history_scroll = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scroll.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        history_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # 刷新按钮
        refresh_btn = ttk.Button(history_frame, text="刷新", command=self.refresh_history)
        refresh_btn.pack(side=tk.BOTTOM, pady=10)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出Excel", command=self.export_excel)
        file_menu.add_command(label="导出JSON", command=self.export_json)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据库备份", command=self.backup_database)
        tools_menu.add_command(label="清空缓存", command=self.clear_cache)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def login_action(self):
        """登录操作"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not username or not password:
            messagebox.showerror("错误", "请输入用户名和密码")
            return

        # 在新线程中执行登录
        def login_thread():
            try:
                self.login_status_var.set("正在登录...")
                self.login_btn.config(state=tk.DISABLED)

                if self.crawler.login(username, password):
                    self.current_user = username
                    self.login_status_var.set(f"已登录: {username}")
                    self.login_btn.config(state=tk.DISABLED)
                    self.logout_btn.config(state=tk.NORMAL)
                    self.status_var.set(f"用户 {username} 登录成功")

                    # 切换到数据选择页面
                    self.notebook.select(1)
                else:
                    self.login_status_var.set("登录失败")
                    self.login_btn.config(state=tk.NORMAL)
                    messagebox.showerror("登录失败", "用户名或密码错误，请重试")

            except Exception as e:
                self.logger.error(f"登录异常: {e}")
                self.login_status_var.set("登录异常")
                self.login_btn.config(state=tk.NORMAL)
                messagebox.showerror("登录异常", f"登录过程中发生错误: {str(e)}")

        threading.Thread(target=login_thread, daemon=True).start()

    def logout_action(self):
        """注销操作"""
        try:
            self.crawler.login_manager.logout()
            self.current_user = None
            self.login_status_var.set("未登录")
            self.login_btn.config(state=tk.NORMAL)
            self.logout_btn.config(state=tk.DISABLED)
            self.status_var.set("已注销")

            # 清空表单
            self.username_var.set("")
            self.password_var.set("")

            # 切换回登录页面
            self.notebook.select(0)

        except Exception as e:
            self.logger.error(f"注销异常: {e}")
            messagebox.showerror("注销异常", f"注销过程中发生错误: {str(e)}")

    def start_crawling(self):
        """开始爬取数据"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        # 检查选择的数据类型
        selected_types = [k for k, v in self.data_types.items() if v.get()]
        if not selected_types:
            messagebox.showerror("错误", "请至少选择一种数据类型")
            return

        def crawl_thread():
            try:
                self.progress_bar.start()
                self.status_var.set("正在爬取数据...")

                for data_type in selected_types:
                    if data_type == 'personal_info':
                        self.progress_var.set("正在爬取个人信息...")
                        self.crawler.crawl_personal_info()
                    elif data_type == 'applications':
                        self.progress_var.set("正在爬取办事进度...")
                        self.crawler.crawl_applications()
                    elif data_type == 'certificates':
                        self.progress_var.set("正在爬取证书信息...")
                        self.crawler.crawl_certificates()

                self.progress_bar.stop()
                self.progress_var.set("爬取完成")
                self.status_var.set("数据爬取完成")

                # 自动加载数据到显示页面
                self.load_cached_data()

                messagebox.showinfo("完成", "数据爬取完成！")

            except Exception as e:
                self.progress_bar.stop()
                self.progress_var.set("爬取失败")
                self.status_var.set("数据爬取失败")
                self.logger.error(f"爬取数据异常: {e}")
                messagebox.showerror("爬取失败", f"数据爬取过程中发生错误: {str(e)}")

        threading.Thread(target=crawl_thread, daemon=True).start()

    def load_cached_data(self):
        """加载缓存数据"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        try:
            cached_data = self.crawler.get_cached_data(self.current_user)

            # 加载个人信息
            if cached_data['personal_info']:
                self.load_personal_info(cached_data['personal_info'])

            # 加载办事进度
            if cached_data['applications']:
                self.load_applications(cached_data['applications'])

            # 加载证书信息
            if cached_data['certificates']:
                self.load_certificates(cached_data['certificates'])

            # 切换到数据展示页面
            self.notebook.select(2)
            self.status_var.set("缓存数据加载完成")

        except Exception as e:
            self.logger.error(f"加载缓存数据异常: {e}")
            messagebox.showerror("加载失败", f"加载缓存数据时发生错误: {str(e)}")

    def load_personal_info(self, personal_info):
        """加载个人信息到表格"""
        # 清空现有数据
        for item in self.personal_tree.get_children():
            self.personal_tree.delete(item)

        # 添加新数据
        for key, value in personal_info.items():
            if key != 'update_time':
                self.personal_tree.insert('', 'end', values=(key, value))

    def load_applications(self, applications):
        """加载办事进度到表格"""
        # 清空现有数据
        for item in self.apps_tree.get_children():
            self.apps_tree.delete(item)

        # 添加新数据
        for app in applications:
            values = (
                app.get('application_id', ''),
                app.get('application_name', ''),
                app.get('status', ''),
                app.get('submit_time', ''),
                app.get('update_time', '')
            )
            self.apps_tree.insert('', 'end', values=values)

    def load_certificates(self, certificates):
        """加载证书信息到表格"""
        # 清空现有数据
        for item in self.certs_tree.get_children():
            self.certs_tree.delete(item)

        # 添加新数据
        for cert in certificates:
            values = (
                cert.get('cert_name', ''),
                cert.get('cert_number', ''),
                cert.get('issue_date', ''),
                cert.get('status', '')
            )
            self.certs_tree.insert('', 'end', values=values)

    def save_manual_data(self):
        """保存手动录入的数据"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        try:
            # 收集表单数据
            manual_data = {}
            for key, var in self.entry_vars.items():
                value = var.get().strip()
                if value:
                    manual_data[key] = value

            if not manual_data:
                messagebox.showwarning("警告", "请至少填写一个字段")
                return

            # 保存到数据库
            if self.crawler.db_manager.save_user_info(self.current_user, manual_data):
                messagebox.showinfo("成功", "数据保存成功！")
                self.status_var.set("手动数据保存成功")

                # 刷新显示
                self.load_cached_data()
            else:
                messagebox.showerror("失败", "数据保存失败")

        except Exception as e:
            self.logger.error(f"保存手动数据异常: {e}")
            messagebox.showerror("保存失败", f"保存数据时发生错误: {str(e)}")

    def clear_form(self):
        """清空表单"""
        for var in self.entry_vars.values():
            var.set("")
        self.status_var.set("表单已清空")

    def load_current_data(self):
        """加载当前用户数据到表单"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        try:
            user_info = self.crawler.db_manager.get_user_info(self.current_user)
            if user_info:
                for key, var in self.entry_vars.items():
                    if key in user_info and user_info[key]:
                        var.set(user_info[key])
                self.status_var.set("当前数据已加载到表单")
            else:
                messagebox.showinfo("提示", "暂无用户数据")

        except Exception as e:
            self.logger.error(f"加载当前数据异常: {e}")
            messagebox.showerror("加载失败", f"加载数据时发生错误: {str(e)}")

    def refresh_history(self):
        """刷新历史记录"""
        if not self.current_user:
            return

        try:
            # 清空现有数据
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # 获取历史记录
            history = self.crawler.get_crawl_history(self.current_user)

            # 添加到表格
            for record in history:
                values = (
                    record.get('created_time', ''),
                    record.get('action', ''),
                    record.get('status', ''),
                    record.get('message', ''),
                    record.get('data_count', 0)
                )
                self.history_tree.insert('', 'end', values=values)

            self.status_var.set("历史记录已刷新")

        except Exception as e:
            self.logger.error(f"刷新历史记录异常: {e}")
            messagebox.showerror("刷新失败", f"刷新历史记录时发生错误: {str(e)}")

    def export_data(self):
        """导出数据"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        # 选择导出格式
        format_choice = messagebox.askyesnocancel("导出格式", "选择导出格式:\n是 - Excel\n否 - JSON\n取消 - 取消导出")

        if format_choice is None:  # 取消
            return

        try:
            if format_choice:  # Excel
                filename = self.crawler.export_user_data(self.current_user, 'excel')
            else:  # JSON
                filename = self.crawler.export_user_data(self.current_user, 'json')

            if filename:
                messagebox.showinfo("导出成功", f"数据已导出到: {filename}")
                self.status_var.set(f"数据已导出: {filename}")
            else:
                messagebox.showerror("导出失败", "数据导出失败")

        except Exception as e:
            self.logger.error(f"导出数据异常: {e}")
            messagebox.showerror("导出失败", f"导出数据时发生错误: {str(e)}")

    def export_excel(self):
        """导出Excel"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        try:
            filename = self.crawler.export_user_data(self.current_user, 'excel')
            if filename:
                messagebox.showinfo("导出成功", f"Excel文件已导出到: {filename}")
            else:
                messagebox.showerror("导出失败", "Excel导出失败")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出Excel时发生错误: {str(e)}")

    def export_json(self):
        """导出JSON"""
        if not self.current_user:
            messagebox.showerror("错误", "请先登录")
            return

        try:
            filename = self.crawler.export_user_data(self.current_user, 'json')
            if filename:
                messagebox.showinfo("导出成功", f"JSON文件已导出到: {filename}")
            else:
                messagebox.showerror("导出失败", "JSON导出失败")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出JSON时发生错误: {str(e)}")

    def backup_database(self):
        """备份数据库"""
        try:
            backup_file = self.crawler.db_manager.backup_database()
            if backup_file:
                messagebox.showinfo("备份成功", f"数据库已备份到: {backup_file}")
                self.status_var.set(f"数据库已备份: {backup_file}")
            else:
                messagebox.showerror("备份失败", "数据库备份失败")
        except Exception as e:
            messagebox.showerror("备份失败", f"备份数据库时发生错误: {str(e)}")

    def clear_cache(self):
        """清空缓存"""
        if messagebox.askyesno("确认", "确定要清空所有缓存数据吗？此操作不可恢复！"):
            try:
                # 这里可以添加清空缓存的逻辑
                messagebox.showinfo("完成", "缓存已清空")
                self.status_var.set("缓存已清空")
            except Exception as e:
                messagebox.showerror("清空失败", f"清空缓存时发生错误: {str(e)}")

    def show_help(self):
        """显示帮助信息"""
        help_text = """
苏服办爬虫系统使用说明：

1. 登录：在登录页面输入苏服办账号和密码
2. 数据选择：选择要爬取的数据类型，点击"开始爬取"
3. 数据展示：查看爬取到的数据
4. 数据录入：手动录入或编辑个人信息
5. 历史记录：查看爬取历史和操作日志

注意事项：
- 请确保网络连接正常
- 首次使用可能需要处理验证码
- 建议定期备份数据库
        """
        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
苏服办爬虫系统 v1.0

一个用于爬取苏州政务服务网数据的工具

开发时间：2025年9月
技术栈：Python + Tkinter + Selenium + SQLite

功能特性：
- 自动登录苏服办
- 数据选择性爬取
- 图形化界面操作
- 数据本地存储
- 多格式数据导出
        """
        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行主窗口"""
        try:
            self.root.mainloop()
        finally:
            self.crawler.stop()
