"""
苏服办登录模块
"""

import logging
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from .utils import (
    create_driver, wait_for_element, wait_for_clickable, 
    safe_click, random_delay, take_screenshot, handle_alert,
    check_login_status
)
from config.settings import SUFUBAN_CONFIG


class SufubanLogin:
    """苏服办登录类"""
    
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.is_logged_in = False
        self.logger = logging.getLogger(__name__)
    
    def start_driver(self):
        """启动浏览器驱动"""
        try:
            self.driver = create_driver(self.headless)
            self.logger.info("浏览器驱动启动成功")
            return True
        except Exception as e:
            self.logger.error(f"启动浏览器驱动失败: {e}")
            return False
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("浏览器驱动已关闭")
            except Exception as e:
                self.logger.error(f"关闭浏览器驱动失败: {e}")
    
    def navigate_to_login_page(self):
        """导航到登录页面"""
        try:
            self.logger.info("正在访问苏服办主页...")
            self.driver.get(SUFUBAN_CONFIG['base_url'])
            random_delay(2, 4)
            
            # 查找登录按钮或链接
            login_selectors = [
                "//a[contains(text(), '登录')]",
                "//a[contains(text(), '登錄')]", 
                "//button[contains(text(), '登录')]",
                "//*[@id='login']",
                "//*[contains(@class, 'login')]"
            ]
            
            login_element = None
            for selector in login_selectors:
                try:
                    login_element = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if login_element:
                        break
                except:
                    continue
            
            if login_element:
                self.logger.info("找到登录入口，正在点击...")
                safe_click(self.driver, login_element)
                random_delay(2, 4)
                return True
            else:
                self.logger.warning("未找到登录入口，尝试直接访问登录页面")
                self.driver.get(SUFUBAN_CONFIG['login_url'])
                random_delay(2, 4)
                return True
                
        except Exception as e:
            self.logger.error(f"导航到登录页面失败: {e}")
            take_screenshot(self.driver, "login_navigation_error.png")
            return False
    
    def handle_captcha(self):
        """处理验证码"""
        try:
            # 查找验证码图片
            captcha_selectors = [
                "//img[contains(@src, 'captcha')]",
                "//img[contains(@src, 'verify')]",
                "//img[contains(@id, 'captcha')]",
                "//*[contains(@class, 'captcha')]//img"
            ]
            
            captcha_img = None
            for selector in captcha_selectors:
                try:
                    captcha_img = self.driver.find_element(By.XPATH, selector)
                    if captcha_img:
                        break
                except:
                    continue
            
            if captcha_img:
                self.logger.info("检测到验证码，需要人工处理")
                take_screenshot(self.driver, "captcha_required.png")
                
                # 这里可以集成OCR或者提示用户手动输入
                captcha_code = input("请输入验证码: ")
                
                # 查找验证码输入框
                captcha_input_selectors = [
                    "//input[contains(@name, 'captcha')]",
                    "//input[contains(@name, 'verify')]",
                    "//input[contains(@id, 'captcha')]",
                    "//input[contains(@placeholder, '验证码')]"
                ]
                
                captcha_input = None
                for selector in captcha_input_selectors:
                    try:
                        captcha_input = self.driver.find_element(By.XPATH, selector)
                        if captcha_input:
                            break
                    except:
                        continue
                
                if captcha_input:
                    captcha_input.clear()
                    captcha_input.send_keys(captcha_code)
                    self.logger.info("验证码已输入")
                    return True
                else:
                    self.logger.error("未找到验证码输入框")
                    return False
            
            return True  # 没有验证码
            
        except Exception as e:
            self.logger.error(f"处理验证码失败: {e}")
            return False
    
    def login(self, username, password):
        """执行登录操作"""
        try:
            if not self.driver:
                if not self.start_driver():
                    return False
            
            # 导航到登录页面
            if not self.navigate_to_login_page():
                return False
            
            self.logger.info("开始登录流程...")
            
            # 查找用户名输入框
            username_selectors = [
                "//input[@name='username']",
                "//input[@name='userName']", 
                "//input[@name='loginName']",
                "//input[@id='username']",
                "//input[@id='userName']",
                "//input[contains(@placeholder, '用户名')]",
                "//input[contains(@placeholder, '账号')]"
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = wait_for_element(self.driver, By.XPATH, selector, 5)
                    if username_input:
                        break
                except:
                    continue
            
            if not username_input:
                self.logger.error("未找到用户名输入框")
                take_screenshot(self.driver, "username_input_not_found.png")
                return False
            
            # 查找密码输入框
            password_selectors = [
                "//input[@name='password']",
                "//input[@name='passWord']",
                "//input[@id='password']",
                "//input[@type='password']",
                "//input[contains(@placeholder, '密码')]"
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = wait_for_element(self.driver, By.XPATH, selector, 5)
                    if password_input:
                        break
                except:
                    continue
            
            if not password_input:
                self.logger.error("未找到密码输入框")
                take_screenshot(self.driver, "password_input_not_found.png")
                return False
            
            # 输入用户名和密码
            self.logger.info("输入登录凭据...")
            username_input.clear()
            username_input.send_keys(username)
            random_delay(1, 2)
            
            password_input.clear()
            password_input.send_keys(password)
            random_delay(1, 2)
            
            # 处理验证码
            if not self.handle_captcha():
                return False
            
            # 查找登录按钮
            login_button_selectors = [
                "//button[contains(text(), '登录')]",
                "//button[contains(text(), '登錄')]",
                "//input[@type='submit']",
                "//button[@type='submit']",
                "//*[@id='loginBtn']",
                "//*[contains(@class, 'login-btn')]"
            ]
            
            login_button = None
            for selector in login_button_selectors:
                try:
                    login_button = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if login_button:
                        break
                except:
                    continue
            
            if not login_button:
                self.logger.error("未找到登录按钮")
                take_screenshot(self.driver, "login_button_not_found.png")
                return False
            
            # 点击登录按钮
            self.logger.info("点击登录按钮...")
            safe_click(self.driver, login_button)
            random_delay(3, 5)
            
            # 处理可能的弹窗
            handle_alert(self.driver)
            
            # 检查登录结果
            if self.check_login_success():
                self.is_logged_in = True
                self.logger.info("登录成功！")
                return True
            else:
                self.logger.error("登录失败")
                take_screenshot(self.driver, "login_failed.png")
                return False
                
        except Exception as e:
            self.logger.error(f"登录过程中发生错误: {e}")
            take_screenshot(self.driver, "login_error.png")
            return False
    
    def check_login_success(self):
        """检查登录是否成功"""
        try:
            # 等待页面加载
            time.sleep(3)
            
            # 检查是否跳转到用户中心或主页
            current_url = self.driver.current_url
            self.logger.info(f"当前页面URL: {current_url}")
            
            # 检查登录状态
            if check_login_status(self.driver):
                return True
            
            # 检查是否有错误提示
            error_selectors = [
                "//*[contains(text(), '用户名或密码错误')]",
                "//*[contains(text(), '登录失败')]",
                "//*[contains(text(), '验证码错误')]",
                "//*[contains(@class, 'error')]"
            ]
            
            for selector in error_selectors:
                try:
                    error_element = self.driver.find_element(By.XPATH, selector)
                    if error_element and error_element.is_displayed():
                        error_text = error_element.text
                        self.logger.error(f"登录错误: {error_text}")
                        return False
                except:
                    continue
            
            # 如果URL包含登录相关关键词，说明还在登录页面
            if any(keyword in current_url.lower() for keyword in ['login', 'signin', 'auth']):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def logout(self):
        """退出登录"""
        try:
            if not self.is_logged_in:
                return True
            
            logout_selectors = [
                "//a[contains(text(), '退出')]",
                "//a[contains(text(), '注销')]",
                "//a[contains(text(), '登出')]",
                "//*[contains(@class, 'logout')]"
            ]
            
            logout_element = None
            for selector in logout_selectors:
                try:
                    logout_element = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if logout_element:
                        break
                except:
                    continue
            
            if logout_element:
                safe_click(self.driver, logout_element)
                random_delay(2, 3)
                self.is_logged_in = False
                self.logger.info("已退出登录")
                return True
            else:
                self.logger.warning("未找到退出登录按钮")
                return False
                
        except Exception as e:
            self.logger.error(f"退出登录失败: {e}")
            return False
