"""
苏服办爬虫系统主窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import logging
from datetime import datetime
from config.settings import GUI_CONFIG
from crawler.login import SufubanLogin
from crawler.data_crawler import SufubanDataCrawler
from database.manager import get_db_manager


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_ui()
        self.setup_logging()
        
        # 爬虫相关
        self.login_instance = None
        self.crawler_instance = None
        self.db_manager = get_db_manager()
        
        # 当前用户
        self.current_user = None
        
    def setup_window(self):
        """设置窗口属性"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
    
    def setup_variables(self):
        """设置变量"""
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.status_var = tk.StringVar(value="就绪")
        self.progress_var = tk.DoubleVar()
        self.headless_var = tk.BooleanVar(value=True)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 登录区域
        self.create_login_section(main_frame)
        
        # 操作区域
        self.create_operation_section(main_frame)
        
        # 数据显示区域
        self.create_data_section(main_frame)
        
        # 状态栏
        self.create_status_section(main_frame)
    
    def create_login_section(self, parent):
        """创建登录区域"""
        login_frame = ttk.LabelFrame(parent, text="登录信息", padding="10")
        login_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 用户名
        ttk.Label(login_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        username_entry = ttk.Entry(login_frame, textvariable=self.username_var, width=20)
        username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 密码
        ttk.Label(login_frame, text="密码:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        password_entry = ttk.Entry(login_frame, textvariable=self.password_var, show="*", width=20)
        password_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 无头模式
        headless_check = ttk.Checkbutton(login_frame, text="无头模式", variable=self.headless_var)
        headless_check.grid(row=0, column=4, padx=(0, 10))
        
        # 登录按钮
        self.login_btn = ttk.Button(login_frame, text="登录", command=self.login)
        self.login_btn.grid(row=0, column=5, padx=(0, 5))
        
        # 注销按钮
        self.logout_btn = ttk.Button(login_frame, text="注销", command=self.logout, state=tk.DISABLED)
        self.logout_btn.grid(row=0, column=6)
        
        # 配置列权重
        login_frame.columnconfigure(1, weight=1)
        login_frame.columnconfigure(3, weight=1)
    
    def create_operation_section(self, parent):
        """创建操作区域"""
        op_frame = ttk.LabelFrame(parent, text="操作功能", padding="10")
        op_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮组
        buttons = [
            ("查询个人信息", self.get_personal_info),
            ("查询办事进度", self.get_applications),
            ("查询证书信息", self.get_certificates),
            ("获取所有数据", self.get_all_data),
            ("导出Excel", self.export_excel),
            ("数据库备份", self.backup_database)
        ]
        
        for i, (text, command) in enumerate(buttons):
            btn = ttk.Button(op_frame, text=text, command=command, state=tk.DISABLED)
            btn.grid(row=i//3, column=i%3, padx=5, pady=5, sticky=(tk.W, tk.E))
            setattr(self, f"{text.replace(' ', '_').lower()}_btn", btn)
        
        # 配置列权重
        for i in range(3):
            op_frame.columnconfigure(i, weight=1)
    
    def create_data_section(self, parent):
        """创建数据显示区域"""
        data_frame = ttk.LabelFrame(parent, text="数据显示", padding="10")
        data_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(0, weight=1)
        
        # 创建Notebook
        self.notebook = ttk.Notebook(data_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 个人信息标签页
        self.create_personal_info_tab()
        
        # 办事进度标签页
        self.create_applications_tab()
        
        # 证书信息标签页
        self.create_certificates_tab()
        
        # 日志标签页
        self.create_log_tab()
    
    def create_personal_info_tab(self):
        """创建个人信息标签页"""
        personal_frame = ttk.Frame(self.notebook)
        self.notebook.add(personal_frame, text="个人信息")
        
        # 创建文本框显示个人信息
        self.personal_text = tk.Text(personal_frame, wrap=tk.WORD, state=tk.DISABLED)
        personal_scrollbar = ttk.Scrollbar(personal_frame, orient=tk.VERTICAL, command=self.personal_text.yview)
        self.personal_text.configure(yscrollcommand=personal_scrollbar.set)
        
        self.personal_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        personal_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        personal_frame.columnconfigure(0, weight=1)
        personal_frame.rowconfigure(0, weight=1)
    
    def create_applications_tab(self):
        """创建办事进度标签页"""
        app_frame = ttk.Frame(self.notebook)
        self.notebook.add(app_frame, text="办事进度")
        
        # 创建树形视图
        columns = ("序号", "事项名称", "状态", "申请时间", "更新时间", "受理部门")
        self.app_tree = ttk.Treeview(app_frame, columns=columns, show="headings")
        
        for col in columns:
            self.app_tree.heading(col, text=col)
            self.app_tree.column(col, width=100)
        
        app_scrollbar = ttk.Scrollbar(app_frame, orient=tk.VERTICAL, command=self.app_tree.yview)
        self.app_tree.configure(yscrollcommand=app_scrollbar.set)
        
        self.app_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        app_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        app_frame.columnconfigure(0, weight=1)
        app_frame.rowconfigure(0, weight=1)
    
    def create_certificates_tab(self):
        """创建证书信息标签页"""
        cert_frame = ttk.Frame(self.notebook)
        self.notebook.add(cert_frame, text="证书信息")
        
        # 创建树形视图
        columns = ("证书名称", "证书编号", "发证日期", "状态", "发证机关")
        self.cert_tree = ttk.Treeview(cert_frame, columns=columns, show="headings")
        
        for col in columns:
            self.cert_tree.heading(col, text=col)
            self.cert_tree.column(col, width=120)
        
        cert_scrollbar = ttk.Scrollbar(cert_frame, orient=tk.VERTICAL, command=self.cert_tree.yview)
        self.cert_tree.configure(yscrollcommand=cert_scrollbar.set)
        
        self.cert_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        cert_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        cert_frame.columnconfigure(0, weight=1)
        cert_frame.rowconfigure(0, weight=1)
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="操作日志")
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def create_status_section(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, mode='determinate')
        self.progress_bar.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
    
    def setup_logging(self):
        """设置日志处理"""
        # 创建自定义日志处理器，将日志输出到GUI
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record)
                self.text_widget.config(state=tk.NORMAL)
                self.text_widget.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {msg}\n")
                self.text_widget.see(tk.END)
                self.text_widget.config(state=tk.DISABLED)
        
        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        gui_handler.setFormatter(formatter)
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(gui_handler)
    
    def update_status(self, message, progress=None):
        """更新状态"""
        self.status_var.set(message)
        if progress is not None:
            self.progress_var.set(progress)
        self.root.update_idletasks()
    
    def enable_operation_buttons(self, enabled=True):
        """启用/禁用操作按钮"""
        state = tk.NORMAL if enabled else tk.DISABLED
        buttons = [
            'get_personal_info_btn', 'get_applications_btn', 'get_certificates_btn',
            'get_all_data_btn', 'export_excel_btn', 'backup_database_btn'
        ]
        
        for btn_name in buttons:
            if hasattr(self, btn_name):
                getattr(self, btn_name).config(state=state)
    
    def login(self):
        """登录操作"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            messagebox.showerror("错误", "请输入用户名和密码")
            return
        
        def login_thread():
            try:
                self.update_status("正在登录...", 20)
                self.login_btn.config(state=tk.DISABLED)
                
                # 创建登录实例
                self.login_instance = SufubanLogin(headless=self.headless_var.get())
                
                self.update_status("启动浏览器...", 40)
                if not self.login_instance.start_driver():
                    raise Exception("启动浏览器失败")
                
                self.update_status("执行登录...", 60)
                if self.login_instance.login(username, password):
                    self.current_user = username
                    self.crawler_instance = SufubanDataCrawler(self.login_instance)
                    
                    self.update_status("登录成功", 100)
                    
                    # 更新UI状态
                    self.root.after(0, self.on_login_success)
                else:
                    raise Exception("登录失败")
                    
            except Exception as e:
                self.update_status(f"登录失败: {str(e)}", 0)
                self.root.after(0, self.on_login_failed)
        
        # 在新线程中执行登录
        threading.Thread(target=login_thread, daemon=True).start()
    
    def on_login_success(self):
        """登录成功后的UI更新"""
        self.login_btn.config(state=tk.DISABLED)
        self.logout_btn.config(state=tk.NORMAL)
        self.enable_operation_buttons(True)
        messagebox.showinfo("成功", "登录成功！")
    
    def on_login_failed(self):
        """登录失败后的UI更新"""
        self.login_btn.config(state=tk.NORMAL)
        self.logout_btn.config(state=tk.DISABLED)
        self.enable_operation_buttons(False)
        if self.login_instance:
            self.login_instance.close_driver()
            self.login_instance = None
    
    def logout(self):
        """注销操作"""
        try:
            if self.login_instance:
                self.login_instance.logout()
                self.login_instance.close_driver()
                self.login_instance = None
            
            self.crawler_instance = None
            self.current_user = None
            
            # 更新UI状态
            self.login_btn.config(state=tk.NORMAL)
            self.logout_btn.config(state=tk.DISABLED)
            self.enable_operation_buttons(False)
            
            self.update_status("已注销", 0)
            messagebox.showinfo("成功", "已注销登录")
            
        except Exception as e:
            messagebox.showerror("错误", f"注销失败: {str(e)}")
    
    def run(self):
        """运行主窗口"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            logging.error(f"运行主窗口失败: {e}")
    
    def on_closing(self):
        """窗口关闭事件"""
        try:
            if self.login_instance:
                self.login_instance.close_driver()
            self.root.destroy()
        except:
            pass
