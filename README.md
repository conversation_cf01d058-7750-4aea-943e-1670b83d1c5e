# 苏服办爬虫系统

## 项目简介
这是一个用于爬取苏州政务服务网（苏服办）数据的Python爬虫系统，提供图形化界面，支持用户登录、数据查询、信息管理等功能。

## 功能特性
- 用户登录认证
- 个人信息查询
- 办事进度查询
- 证书信息管理
- 数据导出功能
- 图形化操作界面

## 技术栈
- Python 3.8+
- Requests (HTTP请求)
- Selenium (浏览器自动化)
- BeautifulSoup4 (HTML解析)
- Tkinter (GUI界面)
- SQLite (数据存储)
- Pandas (数据处理)

## 安装说明

### 1. 环境要求
- Python 3.8 或更高版本
- Chrome 浏览器

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

## 项目结构
```
sufuban/
├── main.py                 # 主程序入口
├── config/                 # 配置文件
│   ├── __init__.py
│   └── settings.py
├── crawler/                # 爬虫核心模块
│   ├── __init__.py
│   ├── login.py           # 登录模块
│   ├── data_crawler.py    # 数据爬取模块
│   └── utils.py           # 工具函数
├── gui/                    # 图形界面模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口
│   └── components.py      # UI组件
├── database/               # 数据库模块
│   ├── __init__.py
│   ├── models.py          # 数据模型
│   └── manager.py         # 数据管理
├── logs/                   # 日志文件目录
├── data/                   # 数据文件目录
└── tests/                  # 测试文件
    ├── __init__.py
    └── test_crawler.py
```

## 使用说明
1. 启动程序后，在登录界面输入苏服办账号和密码
2. 登录成功后可以进行各种数据查询操作
3. 查询结果会显示在表格中，支持导出为Excel文件
4. 程序会自动保存查询历史和用户数据

## 注意事项
- 请遵守网站使用条款，合理使用爬虫功能
- 建议设置适当的请求间隔，避免对服务器造成压力
- 定期更新程序以适应网站结构变化

## 更新日志
- v1.0.0: 初始版本，基本功能实现
