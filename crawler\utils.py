"""
苏服办爬虫工具函数
"""

import time
import random
import logging
try:
    from fake_useragent import UserAgent
except ImportError:
    UserAgent = None
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from config.settings import CRAWLER_CONFIG


def get_random_user_agent():
    """获取随机User-Agent"""
    try:
        if UserAgent:
            ua = UserAgent()
            return ua.random
        else:
            return CRAWLER_CONFIG['user_agent']
    except:
        return CRAWLER_CONFIG['user_agent']


def create_driver(headless=True):
    """创建Chrome WebDriver"""
    options = Options()
    
    if headless:
        options.add_argument('--headless')
    
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument(f'--user-agent={get_random_user_agent()}')
    
    # 禁用图片加载以提高速度
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2
    }
    options.add_experimental_option("prefs", prefs)
    
    try:
        driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
            options=options
        )
        driver.set_page_load_timeout(CRAWLER_CONFIG['timeout'])
        return driver
    except Exception as e:
        logging.error(f"创建WebDriver失败: {e}")
        raise


def wait_for_element(driver, by, value, timeout=10):
    """等待元素出现"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except Exception as e:
        logging.error(f"等待元素失败: {by}={value}, 错误: {e}")
        return None


def wait_for_clickable(driver, by, value, timeout=10):
    """等待元素可点击"""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
        return element
    except Exception as e:
        logging.error(f"等待可点击元素失败: {by}={value}, 错误: {e}")
        return None


def safe_click(driver, element):
    """安全点击元素"""
    try:
        driver.execute_script("arguments[0].click();", element)
        return True
    except Exception as e:
        logging.error(f"点击元素失败: {e}")
        return False


def random_delay(min_seconds=1, max_seconds=3):
    """随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def take_screenshot(driver, filename):
    """截图保存"""
    try:
        driver.save_screenshot(f"logs/{filename}")
        logging.info(f"截图已保存: {filename}")
    except Exception as e:
        logging.error(f"截图失败: {e}")


def handle_alert(driver):
    """处理弹窗"""
    try:
        alert = driver.switch_to.alert
        alert_text = alert.text
        alert.accept()
        logging.info(f"处理弹窗: {alert_text}")
        return alert_text
    except:
        return None


def get_page_source_safe(driver):
    """安全获取页面源码"""
    try:
        return driver.page_source
    except Exception as e:
        logging.error(f"获取页面源码失败: {e}")
        return ""


def scroll_to_bottom(driver):
    """滚动到页面底部"""
    try:
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
    except Exception as e:
        logging.error(f"滚动页面失败: {e}")


def check_login_status(driver):
    """检查登录状态"""
    try:
        # 检查是否存在登录相关的元素
        login_indicators = [
            "//a[contains(text(), '退出')]",
            "//a[contains(text(), '注销')]",
            "//span[contains(text(), '欢迎')]",
            "//*[contains(@class, 'user-info')]"
        ]
        
        for indicator in login_indicators:
            try:
                element = driver.find_element(By.XPATH, indicator)
                if element:
                    return True
            except:
                continue
        
        return False
    except Exception as e:
        logging.error(f"检查登录状态失败: {e}")
        return False
