"""
苏服办数据爬取模块
"""

import logging
import time
import json
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from .utils import (
    wait_for_element, wait_for_clickable, safe_click, 
    random_delay, take_screenshot, scroll_to_bottom
)


class SufubanDataCrawler:
    """苏服办数据爬取类"""
    
    def __init__(self, login_instance):
        self.login = login_instance
        self.driver = login_instance.driver
        self.logger = logging.getLogger(__name__)
        self.data_cache = {}
    
    def get_personal_info(self):
        """获取个人信息"""
        try:
            if not self.login.is_logged_in:
                self.logger.error("用户未登录，无法获取个人信息")
                return None
            
            self.logger.info("开始获取个人信息...")
            
            # 导航到个人中心
            personal_center_selectors = [
                "//a[contains(text(), '个人中心')]",
                "//a[contains(text(), '我的信息')]",
                "//a[contains(text(), '个人信息')]",
                "//*[contains(@class, 'user-center')]"
            ]
            
            center_element = None
            for selector in personal_center_selectors:
                try:
                    center_element = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if center_element:
                        break
                except:
                    continue
            
            if center_element:
                safe_click(self.driver, center_element)
                random_delay(2, 4)
            
            # 提取个人信息
            personal_info = {}
            
            # 常见的个人信息字段
            info_fields = {
                'name': ['姓名', '真实姓名', '用户姓名'],
                'id_card': ['身份证号', '证件号码', '身份证'],
                'phone': ['手机号', '联系电话', '电话号码'],
                'email': ['邮箱', '电子邮箱', '邮件地址'],
                'address': ['地址', '联系地址', '居住地址'],
                'gender': ['性别'],
                'birth_date': ['出生日期', '生日'],
                'nationality': ['民族'],
                'education': ['学历', '教育程度']
            }
            
            for field_key, field_names in info_fields.items():
                for field_name in field_names:
                    try:
                        # 尝试多种选择器模式
                        selectors = [
                            f"//td[contains(text(), '{field_name}')]/following-sibling::td",
                            f"//span[contains(text(), '{field_name}')]/following-sibling::span",
                            f"//label[contains(text(), '{field_name}')]/following-sibling::input/@value",
                            f"//*[contains(text(), '{field_name}:')]/following-sibling::*"
                        ]
                        
                        for selector in selectors:
                            try:
                                element = self.driver.find_element(By.XPATH, selector)
                                if element:
                                    value = element.text or element.get_attribute('value')
                                    if value and value.strip():
                                        personal_info[field_key] = value.strip()
                                        break
                            except:
                                continue
                        
                        if field_key in personal_info:
                            break
                            
                    except:
                        continue
            
            if personal_info:
                personal_info['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.data_cache['personal_info'] = personal_info
                self.logger.info(f"成功获取个人信息: {len(personal_info)} 个字段")
                return personal_info
            else:
                self.logger.warning("未能获取到个人信息")
                return None
                
        except Exception as e:
            self.logger.error(f"获取个人信息失败: {e}")
            take_screenshot(self.driver, "personal_info_error.png")
            return None
    
    def get_application_progress(self):
        """获取办事进度"""
        try:
            if not self.login.is_logged_in:
                self.logger.error("用户未登录，无法获取办事进度")
                return None
            
            self.logger.info("开始获取办事进度...")
            
            # 导航到办事进度页面
            progress_selectors = [
                "//a[contains(text(), '办事进度')]",
                "//a[contains(text(), '进度查询')]",
                "//a[contains(text(), '我的办件')]",
                "//*[contains(@href, 'progress')]"
            ]
            
            progress_element = None
            for selector in progress_selectors:
                try:
                    progress_element = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if progress_element:
                        break
                except:
                    continue
            
            if progress_element:
                safe_click(self.driver, progress_element)
                random_delay(2, 4)
            
            # 等待表格加载
            table_selectors = [
                "//table",
                "//*[contains(@class, 'table')]",
                "//*[contains(@class, 'list')]"
            ]
            
            table_element = None
            for selector in table_selectors:
                try:
                    table_element = wait_for_element(self.driver, By.XPATH, selector, 10)
                    if table_element:
                        break
                except:
                    continue
            
            if not table_element:
                self.logger.warning("未找到办事进度表格")
                return []
            
            # 提取表格数据
            applications = []
            
            try:
                # 获取表头
                headers = []
                header_elements = self.driver.find_elements(By.XPATH, "//th | //thead//td")
                for header in header_elements:
                    if header.text.strip():
                        headers.append(header.text.strip())
                
                # 获取数据行
                row_elements = self.driver.find_elements(By.XPATH, "//tbody//tr | //table//tr[position()>1]")
                
                for row in row_elements:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) > 0:
                        application = {}
                        for i, cell in enumerate(cells):
                            header_key = headers[i] if i < len(headers) else f"column_{i}"
                            application[header_key] = cell.text.strip()
                        
                        if application:
                            applications.append(application)
                
                if applications:
                    self.data_cache['applications'] = applications
                    self.logger.info(f"成功获取办事进度: {len(applications)} 条记录")
                    return applications
                else:
                    self.logger.info("暂无办事进度记录")
                    return []
                    
            except Exception as e:
                self.logger.error(f"解析办事进度表格失败: {e}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取办事进度失败: {e}")
            take_screenshot(self.driver, "application_progress_error.png")
            return None
    
    def get_certificates(self):
        """获取证书信息"""
        try:
            if not self.login.is_logged_in:
                self.logger.error("用户未登录，无法获取证书信息")
                return None
            
            self.logger.info("开始获取证书信息...")
            
            # 导航到证书页面
            cert_selectors = [
                "//a[contains(text(), '证书')]",
                "//a[contains(text(), '我的证书')]",
                "//a[contains(text(), '证照')]",
                "//*[contains(@href, 'certificate')]"
            ]
            
            cert_element = None
            for selector in cert_selectors:
                try:
                    cert_element = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                    if cert_element:
                        break
                except:
                    continue
            
            if cert_element:
                safe_click(self.driver, cert_element)
                random_delay(2, 4)
            
            certificates = []
            
            # 查找证书列表
            cert_list_selectors = [
                "//*[contains(@class, 'cert-list')]//div",
                "//*[contains(@class, 'certificate')]//div",
                "//table//tr[position()>1]"
            ]
            
            cert_elements = []
            for selector in cert_list_selectors:
                try:
                    cert_elements = self.driver.find_elements(By.XPATH, selector)
                    if cert_elements:
                        break
                except:
                    continue
            
            for cert_element in cert_elements:
                try:
                    cert_info = {}
                    
                    # 提取证书名称
                    name_element = cert_element.find_element(By.XPATH, ".//*[contains(@class, 'name')] | .//h3 | .//strong")
                    if name_element:
                        cert_info['name'] = name_element.text.strip()
                    
                    # 提取证书编号
                    number_element = cert_element.find_element(By.XPATH, ".//*[contains(text(), '编号')] | .//*[contains(text(), '证书号')]")
                    if number_element:
                        cert_info['number'] = number_element.text.strip()
                    
                    # 提取发证日期
                    date_element = cert_element.find_element(By.XPATH, ".//*[contains(text(), '日期')] | .//*[contains(text(), '时间')]")
                    if date_element:
                        cert_info['issue_date'] = date_element.text.strip()
                    
                    # 提取状态
                    status_element = cert_element.find_element(By.XPATH, ".//*[contains(@class, 'status')] | .//*[contains(text(), '有效')]")
                    if status_element:
                        cert_info['status'] = status_element.text.strip()
                    
                    if cert_info:
                        certificates.append(cert_info)
                        
                except:
                    continue
            
            if certificates:
                self.data_cache['certificates'] = certificates
                self.logger.info(f"成功获取证书信息: {len(certificates)} 个证书")
                return certificates
            else:
                self.logger.info("暂无证书信息")
                return []
                
        except Exception as e:
            self.logger.error(f"获取证书信息失败: {e}")
            take_screenshot(self.driver, "certificates_error.png")
            return None
    
    def search_services(self, keyword=""):
        """搜索服务事项"""
        try:
            if not self.login.is_logged_in:
                self.logger.error("用户未登录，无法搜索服务")
                return None
            
            self.logger.info(f"开始搜索服务: {keyword}")
            
            # 查找搜索框
            search_selectors = [
                "//input[@type='search']",
                "//input[contains(@placeholder, '搜索')]",
                "//input[contains(@name, 'search')]",
                "//*[@id='searchInput']"
            ]
            
            search_input = None
            for selector in search_selectors:
                try:
                    search_input = wait_for_element(self.driver, By.XPATH, selector, 5)
                    if search_input:
                        break
                except:
                    continue
            
            if search_input and keyword:
                search_input.clear()
                search_input.send_keys(keyword)
                
                # 查找搜索按钮
                search_button_selectors = [
                    "//button[contains(text(), '搜索')]",
                    "//input[@type='submit']",
                    "//*[@id='searchBtn']"
                ]
                
                search_button = None
                for selector in search_button_selectors:
                    try:
                        search_button = wait_for_clickable(self.driver, By.XPATH, selector, 5)
                        if search_button:
                            break
                    except:
                        continue
                
                if search_button:
                    safe_click(self.driver, search_button)
                    random_delay(2, 4)
            
            # 提取搜索结果
            services = []
            
            result_selectors = [
                "//*[contains(@class, 'service-item')]",
                "//*[contains(@class, 'result-item')]",
                "//table//tr[position()>1]"
            ]
            
            result_elements = []
            for selector in result_selectors:
                try:
                    result_elements = self.driver.find_elements(By.XPATH, selector)
                    if result_elements:
                        break
                except:
                    continue
            
            for element in result_elements:
                try:
                    service_info = {}
                    
                    # 提取服务名称
                    name_element = element.find_element(By.XPATH, ".//a | .//h3 | .//strong")
                    if name_element:
                        service_info['name'] = name_element.text.strip()
                        service_info['link'] = name_element.get_attribute('href')
                    
                    # 提取描述
                    desc_element = element.find_element(By.XPATH, ".//*[contains(@class, 'desc')] | .//p")
                    if desc_element:
                        service_info['description'] = desc_element.text.strip()
                    
                    if service_info:
                        services.append(service_info)
                        
                except:
                    continue
            
            self.logger.info(f"搜索到 {len(services)} 个服务")
            return services
            
        except Exception as e:
            self.logger.error(f"搜索服务失败: {e}")
            take_screenshot(self.driver, "search_services_error.png")
            return None
    
    def get_all_data(self):
        """获取所有数据"""
        try:
            self.logger.info("开始获取所有数据...")
            
            all_data = {
                'personal_info': self.get_personal_info(),
                'applications': self.get_application_progress(),
                'certificates': self.get_certificates(),
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.logger.info("所有数据获取完成")
            return all_data
            
        except Exception as e:
            self.logger.error(f"获取所有数据失败: {e}")
            return None
    
    def export_data_to_json(self, filename=None):
        """导出数据为JSON格式"""
        try:
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"data/sufuban_data_{timestamp}.json"
            
            data = self.get_all_data()
            if data:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                self.logger.info(f"数据已导出到: {filename}")
                return filename
            else:
                self.logger.error("没有数据可导出")
                return None
                
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            return None
