#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("苏服办爬虫系统简单测试")
    print("=" * 30)
    
    # 测试配置
    try:
        from config.settings import GUI_CONFIG
        print("✓ 配置加载成功")
        print(f"  窗口标题: {GUI_CONFIG['window_title']}")
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return
    
    # 测试数据库
    try:
        from database.models import DatabaseManager
        db = DatabaseManager()
        print("✓ 数据库初始化成功")
        
        # 简单测试
        test_data = {'name': '测试', 'phone': '123456'}
        db.save_user_info('test', test_data)
        result = db.get_user_info('test')
        if result:
            print("✓ 数据库读写测试成功")
        else:
            print("✗ 数据库读写测试失败")
            
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return
    
    # 测试GUI基础
    try:
        import tkinter as tk
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        label = tk.Label(root, text="苏服办爬虫系统\n基础功能正常")
        label.pack(expand=True)
        
        button = tk.Button(root, text="关闭", command=root.quit)
        button.pack(pady=10)
        
        print("✓ GUI基础功能正常")
        print("\n测试窗口已打开，请关闭窗口继续...")
        
        root.mainloop()
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        return
    
    print("\n" + "=" * 30)
    print("✓ 基础功能测试完成！")
    print("\n项目结构已创建完成，包含以下功能：")
    print("- 用户登录模块")
    print("- 数据爬取模块") 
    print("- 图形界面模块")
    print("- 数据库存储模块")
    print("- 数据导出功能")
    print("\n注意：由于Python版本兼容性问题，")
    print("某些依赖包可能需要更新版本。")

if __name__ == "__main__":
    main()
