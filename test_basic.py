#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from config.settings import GUI_CONFIG, CRAWLER_CONFIG
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from database.models import DatabaseManager
        print("✓ 数据库模块导入成功")
    except Exception as e:
        print(f"✗ 数据库模块导入失败: {e}")
        return False
    
    try:
        from gui.sufuban_gui import SufubanGUI
        print("✓ GUI模块导入成功")
    except Exception as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False
    
    return True

def test_database():
    """测试数据库功能"""
    print("\n测试数据库功能...")
    
    try:
        from database.models import DatabaseManager
        db = DatabaseManager()
        
        # 测试保存用户信息
        test_data = {
            'name': '测试用户',
            'phone': '13800138000',
            'email': '<EMAIL>'
        }
        
        result = db.save_user_info('test_user', test_data)
        if result:
            print("✓ 用户信息保存成功")
        else:
            print("✗ 用户信息保存失败")
            return False
        
        # 测试获取用户信息
        user_info = db.get_user_info('test_user')
        if user_info and user_info['name'] == '测试用户':
            print("✓ 用户信息获取成功")
        else:
            print("✗ 用户信息获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n测试GUI创建...")
    
    try:
        from gui.sufuban_gui import SufubanGUI
        
        # 只创建GUI对象，不运行
        app = SufubanGUI()
        print("✓ GUI对象创建成功")
        
        # 清理
        if hasattr(app, 'crawler'):
            app.crawler.stop()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("苏服办爬虫系统基本功能测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    # 测试模块导入
    if test_imports():
        success_count += 1
    
    # 测试数据库
    if test_database():
        success_count += 1
    
    # 测试GUI创建
    if test_gui_creation():
        success_count += 1
    
    print("\n" + "=" * 40)
    print(f"测试完成: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("✓ 所有基本功能测试通过！")
        print("\n可以运行以下命令启动程序:")
        print("python main.py")
        return True
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
