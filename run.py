#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
苏服办爬虫系统启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'requests', 'selenium', 'beautifulsoup4', 'lxml', 
        'pandas', 'openpyxl', 'fake_useragent', 'webdriver_manager'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包，请先安装：")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n安装命令：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("苏服办爬虫系统启动中...")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动主程序
        from main import main as main_app
        main_app()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
