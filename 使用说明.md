# 苏服办爬虫系统使用说明

## 项目概述

这是一个专门用于爬取苏州政务服务网（苏服办）数据的Python爬虫系统。系统提供了图形化界面，支持用户登录、数据选择性爬取、数据展示、手动录入和数据导出等功能。

## 主要功能

### 1. 用户登录
- 支持苏服办账号密码登录
- 自动处理验证码（需要人工输入）
- 登录状态管理和会话保持

### 2. 数据选择性爬取
- **个人信息**：姓名、身份证号、手机号、邮箱、地址等
- **办事进度**：申请事项、办理状态、时间节点等
- **证书信息**：证书名称、编号、发证日期、有效期等

### 3. 数据展示
- 分类展示爬取到的数据
- 表格形式清晰呈现
- 支持数据筛选和查看

### 4. 数据录入
- 手动录入个人信息
- 编辑已有数据
- 数据验证和保存

### 5. 数据导出
- 支持Excel格式导出
- 支持JSON格式导出
- 按用户分类导出

### 6. 历史记录
- 爬取操作日志
- 数据变更记录
- 错误日志追踪

## 安装和运行

### 1. 环境要求
- Python 3.8 或更高版本
- Chrome 浏览器
- Windows 10/11 操作系统

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python run.py
```
或者直接运行：
```bash
python main.py
```

## 使用步骤

### 第一步：登录
1. 启动程序后，在"登录"标签页输入苏服办账号和密码
2. 点击"登录"按钮
3. 如果出现验证码，按提示输入
4. 登录成功后会自动跳转到"数据选择"页面

### 第二步：选择数据类型
1. 在"数据选择"页面勾选要爬取的数据类型：
   - ☑ 个人信息
   - ☑ 办事进度  
   - ☑ 证书信息
2. 点击"开始爬取"按钮
3. 等待爬取完成（会显示进度）

### 第三步：查看数据
1. 爬取完成后自动跳转到"数据展示"页面
2. 可以在不同子标签页查看各类数据
3. 也可以点击"查看缓存数据"加载之前爬取的数据

### 第四步：数据录入（可选）
1. 在"数据录入"页面可以手动输入或编辑个人信息
2. 填写完成后点击"保存数据"
3. 可以点击"加载当前数据"将已有数据填入表单

### 第五步：导出数据
1. 在"数据选择"页面点击"导出数据"
2. 选择导出格式（Excel或JSON）
3. 数据将保存到`data/exports/`目录

## 目录结构

```
sufuban/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── 使用说明.md            # 详细使用说明
├── config/                # 配置文件
│   ├── __init__.py
│   └── settings.py        # 系统配置
├── crawler/               # 爬虫核心模块
│   ├── __init__.py
│   ├── sufuban_crawler.py # 主爬虫类
│   ├── login.py          # 登录模块
│   ├── data_crawler.py   # 数据爬取模块
│   └── utils.py          # 工具函数
├── gui/                   # 图形界面模块
│   ├── __init__.py
│   └── sufuban_gui.py    # 主界面
├── database/              # 数据库模块
│   ├── __init__.py
│   ├── models.py         # 数据模型
│   └── manager.py        # 数据管理
├── logs/                  # 日志文件目录
├── data/                  # 数据文件目录
│   ├── exports/          # 导出文件
│   └── backup/           # 数据库备份
└── tests/                 # 测试文件
    ├── __init__.py
    └── test_crawler.py   # 测试用例
```

## 注意事项

### 1. 网络和安全
- 确保网络连接稳定
- 首次使用可能需要下载Chrome驱动
- 程序会自动处理反爬虫机制

### 2. 数据安全
- 所有数据存储在本地SQLite数据库
- 不会上传任何个人信息到外部服务器
- 建议定期备份数据库

### 3. 使用限制
- 请遵守苏服办网站使用条款
- 合理控制爬取频率，避免对服务器造成压力
- 仅用于个人数据查询，不得用于商业用途

### 4. 故障排除
- 如果登录失败，检查账号密码是否正确
- 如果爬取失败，可能是网站结构发生变化
- 查看日志文件获取详细错误信息

## 技术特性

### 1. 智能反爬虫
- 随机User-Agent
- 请求间隔控制
- 自动重试机制
- 异常处理

### 2. 数据管理
- SQLite本地数据库
- 数据增量更新
- 自动备份功能
- 多格式导出

### 3. 用户体验
- 直观的图形界面
- 实时进度显示
- 操作日志记录
- 错误提示友好

## 更新日志

### v1.0.0 (2025-09-02)
- 初始版本发布
- 实现基本爬虫功能
- 完成图形界面
- 支持数据导出

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看日志文件：`logs/sufuban.log`
2. 运行测试用例：`python tests/test_crawler.py`
3. 检查网络连接和浏览器版本

## 免责声明

本工具仅供学习和个人使用，使用者应遵守相关法律法规和网站使用条款。开发者不承担因使用本工具而产生的任何法律责任。
