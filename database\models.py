"""
苏服办数据库模型
"""

import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from config.settings import DATABASE_CONFIG


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self):
        self.db_path = DATABASE_CONFIG['db_path']
        self.logger = logging.getLogger(__name__)
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            # 确保数据目录存在
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建用户信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        name TEXT,
                        id_card TEXT,
                        phone TEXT,
                        email TEXT,
                        address TEXT,
                        gender TEXT,
                        birth_date TEXT,
                        nationality TEXT,
                        education TEXT,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建办事进度表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS applications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT NOT NULL,
                        application_id TEXT,
                        application_name TEXT,
                        status TEXT,
                        submit_time TEXT,
                        update_time TEXT,
                        department TEXT,
                        result TEXT,
                        remarks TEXT,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (username) REFERENCES user_info (username)
                    )
                ''')
                
                # 创建证书信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS certificates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT NOT NULL,
                        cert_name TEXT,
                        cert_number TEXT,
                        issue_date TEXT,
                        expire_date TEXT,
                        status TEXT,
                        issuer TEXT,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (username) REFERENCES user_info (username)
                    )
                ''')
                
                # 创建服务事项表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS services (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        service_name TEXT,
                        service_code TEXT,
                        department TEXT,
                        description TEXT,
                        requirements TEXT,
                        process_time TEXT,
                        fee TEXT,
                        link TEXT,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建爬取日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS crawl_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT,
                        action TEXT,
                        status TEXT,
                        message TEXT,
                        data_count INTEGER DEFAULT 0,
                        created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_user_info(self, username, user_data):
        """保存用户信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查用户是否存在
                cursor.execute('SELECT id FROM user_info WHERE username = ?', (username,))
                existing_user = cursor.fetchone()
                
                if existing_user:
                    # 更新用户信息
                    cursor.execute('''
                        UPDATE user_info SET 
                        name = ?, id_card = ?, phone = ?, email = ?, 
                        address = ?, gender = ?, birth_date = ?, 
                        nationality = ?, education = ?, updated_time = ?
                        WHERE username = ?
                    ''', (
                        user_data.get('name'),
                        user_data.get('id_card'),
                        user_data.get('phone'),
                        user_data.get('email'),
                        user_data.get('address'),
                        user_data.get('gender'),
                        user_data.get('birth_date'),
                        user_data.get('nationality'),
                        user_data.get('education'),
                        datetime.now(),
                        username
                    ))
                    self.logger.info(f"更新用户信息: {username}")
                else:
                    # 插入新用户
                    cursor.execute('''
                        INSERT INTO user_info 
                        (username, name, id_card, phone, email, address, 
                         gender, birth_date, nationality, education)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        username,
                        user_data.get('name'),
                        user_data.get('id_card'),
                        user_data.get('phone'),
                        user_data.get('email'),
                        user_data.get('address'),
                        user_data.get('gender'),
                        user_data.get('birth_date'),
                        user_data.get('nationality'),
                        user_data.get('education')
                    ))
                    self.logger.info(f"新增用户信息: {username}")
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"保存用户信息失败: {e}")
            return False
    
    def save_applications(self, username, applications):
        """保存办事进度"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清除该用户的旧记录
                cursor.execute('DELETE FROM applications WHERE username = ?', (username,))
                
                # 插入新记录
                for app in applications:
                    cursor.execute('''
                        INSERT INTO applications 
                        (username, application_id, application_name, status, 
                         submit_time, update_time, department, result, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        username,
                        app.get('序号') or app.get('申请编号'),
                        app.get('事项名称') or app.get('申请事项'),
                        app.get('状态') or app.get('办理状态'),
                        app.get('申请时间') or app.get('提交时间'),
                        app.get('更新时间') or app.get('办理时间'),
                        app.get('受理部门') or app.get('办理部门'),
                        app.get('办理结果'),
                        app.get('备注')
                    ))
                
                conn.commit()
                self.logger.info(f"保存办事进度: {username}, {len(applications)} 条记录")
                return True
                
        except Exception as e:
            self.logger.error(f"保存办事进度失败: {e}")
            return False
    
    def save_certificates(self, username, certificates):
        """保存证书信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清除该用户的旧记录
                cursor.execute('DELETE FROM certificates WHERE username = ?', (username,))
                
                # 插入新记录
                for cert in certificates:
                    cursor.execute('''
                        INSERT INTO certificates 
                        (username, cert_name, cert_number, issue_date, 
                         expire_date, status, issuer)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        username,
                        cert.get('name') or cert.get('证书名称'),
                        cert.get('number') or cert.get('证书编号'),
                        cert.get('issue_date') or cert.get('发证日期'),
                        cert.get('expire_date') or cert.get('有效期'),
                        cert.get('status') or cert.get('状态'),
                        cert.get('issuer') or cert.get('发证机关')
                    ))
                
                conn.commit()
                self.logger.info(f"保存证书信息: {username}, {len(certificates)} 个证书")
                return True
                
        except Exception as e:
            self.logger.error(f"保存证书信息失败: {e}")
            return False
    
    def save_crawl_log(self, username, action, status, message, data_count=0):
        """保存爬取日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO crawl_logs 
                    (username, action, status, message, data_count)
                    VALUES (?, ?, ?, ?, ?)
                ''', (username, action, status, message, data_count))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"保存爬取日志失败: {e}")
    
    def get_user_info(self, username):
        """获取用户信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM user_info WHERE username = ?', (username,))
                result = cursor.fetchone()
                return dict(result) if result else None
                
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
    
    def get_applications(self, username):
        """获取办事进度"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM applications 
                    WHERE username = ? 
                    ORDER BY created_time DESC
                ''', (username,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
                
        except Exception as e:
            self.logger.error(f"获取办事进度失败: {e}")
            return []
    
    def get_certificates(self, username):
        """获取证书信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM certificates 
                    WHERE username = ? 
                    ORDER BY created_time DESC
                ''', (username,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
                
        except Exception as e:
            self.logger.error(f"获取证书信息失败: {e}")
            return []
    
    def get_crawl_logs(self, username=None, limit=100):
        """获取爬取日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                if username:
                    cursor.execute('''
                        SELECT * FROM crawl_logs 
                        WHERE username = ? 
                        ORDER BY created_time DESC 
                        LIMIT ?
                    ''', (username, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM crawl_logs 
                        ORDER BY created_time DESC 
                        LIMIT ?
                    ''', (limit,))
                
                results = cursor.fetchall()
                return [dict(row) for row in results]
                
        except Exception as e:
            self.logger.error(f"获取爬取日志失败: {e}")
            return []
    
    def export_to_excel(self, username, filename=None):
        """导出数据到Excel"""
        try:
            import pandas as pd
            
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"data/exports/sufuban_data_{username}_{timestamp}.xlsx"
            
            # 确保导出目录存在
            Path(filename).parent.mkdir(parents=True, exist_ok=True)
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 用户信息
                user_info = self.get_user_info(username)
                if user_info:
                    df_user = pd.DataFrame([user_info])
                    df_user.to_excel(writer, sheet_name='用户信息', index=False)
                
                # 办事进度
                applications = self.get_applications(username)
                if applications:
                    df_apps = pd.DataFrame(applications)
                    df_apps.to_excel(writer, sheet_name='办事进度', index=False)
                
                # 证书信息
                certificates = self.get_certificates(username)
                if certificates:
                    df_certs = pd.DataFrame(certificates)
                    df_certs.to_excel(writer, sheet_name='证书信息', index=False)
            
            self.logger.info(f"数据已导出到: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            return None
    
    def backup_database(self):
        """备份数据库"""
        try:
            backup_dir = Path(DATABASE_CONFIG['backup_path'])
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = backup_dir / f"sufuban_backup_{timestamp}.db"
            
            import shutil
            shutil.copy2(self.db_path, backup_file)
            
            self.logger.info(f"数据库已备份到: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            self.logger.error(f"备份数据库失败: {e}")
            return None
