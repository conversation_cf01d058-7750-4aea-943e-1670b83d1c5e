#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
苏服办爬虫系统主程序
Author: AI Assistant
Date: 2025-09-02
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import LOGGING_CONFIG, GUI_CONFIG
from gui.sufuban_gui import SufubanGUI


def setup_logging():
    """设置日志系统"""
    log_dir = Path(LOGGING_CONFIG['log_file']).parent
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, LOGGING_CONFIG['log_level']),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOGGING_CONFIG['log_file'], encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'data',
        'data/exports',
        'data/backup'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("苏服办爬虫系统启动")
        
        # 创建必要目录
        create_directories()
        
        # 启动GUI应用
        app = SufubanGUI()
        app.run()
        
    except Exception as e:
        logging.error(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
