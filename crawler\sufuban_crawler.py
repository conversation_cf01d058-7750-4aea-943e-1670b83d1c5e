"""
苏服办APP数据爬取核心模块
"""

import logging
import time
import json
from datetime import datetime
from selenium.webdriver.common.by import By
from .login import SufubanLogin
from .data_crawler import SufubanDataCrawler
from database.models import DatabaseManager


class SufubanAppCrawler:
    """苏服办APP爬虫主类"""
    
    def __init__(self, headless=True):
        self.headless = headless
        self.login_manager = SufubanLogin(headless)
        self.data_crawler = None
        self.db_manager = DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.current_user = None
    
    def start(self):
        """启动爬虫"""
        try:
            if self.login_manager.start_driver():
                self.data_crawler = SufubanDataCrawler(self.login_manager)
                self.logger.info("苏服办爬虫启动成功")
                return True
            else:
                self.logger.error("苏服办爬虫启动失败")
                return False
        except Exception as e:
            self.logger.error(f"启动爬虫失败: {e}")
            return False
    
    def stop(self):
        """停止爬虫"""
        try:
            if self.login_manager:
                self.login_manager.logout()
                self.login_manager.close_driver()
            self.logger.info("苏服办爬虫已停止")
        except Exception as e:
            self.logger.error(f"停止爬虫失败: {e}")
    
    def login(self, username, password):
        """用户登录"""
        try:
            self.logger.info(f"尝试登录用户: {username}")
            
            if self.login_manager.login(username, password):
                self.current_user = username
                self.db_manager.save_crawl_log(
                    username, "登录", "成功", "用户登录成功"
                )
                return True
            else:
                self.db_manager.save_crawl_log(
                    username, "登录", "失败", "用户登录失败"
                )
                return False
                
        except Exception as e:
            self.logger.error(f"登录失败: {e}")
            self.db_manager.save_crawl_log(
                username, "登录", "错误", f"登录过程出错: {str(e)}"
            )
            return False
    
    def crawl_personal_info(self):
        """爬取个人信息"""
        try:
            if not self.current_user:
                self.logger.error("用户未登录")
                return None
            
            self.logger.info("开始爬取个人信息...")
            personal_info = self.data_crawler.get_personal_info()
            
            if personal_info:
                # 保存到数据库
                self.db_manager.save_user_info(self.current_user, personal_info)
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取个人信息", "成功", 
                    f"成功获取个人信息", len(personal_info)
                )
                return personal_info
            else:
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取个人信息", "失败", "未获取到个人信息"
                )
                return None
                
        except Exception as e:
            self.logger.error(f"爬取个人信息失败: {e}")
            self.db_manager.save_crawl_log(
                self.current_user, "爬取个人信息", "错误", f"爬取过程出错: {str(e)}"
            )
            return None
    
    def crawl_applications(self):
        """爬取办事进度"""
        try:
            if not self.current_user:
                self.logger.error("用户未登录")
                return None
            
            self.logger.info("开始爬取办事进度...")
            applications = self.data_crawler.get_application_progress()
            
            if applications is not None:
                # 保存到数据库
                self.db_manager.save_applications(self.current_user, applications)
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取办事进度", "成功", 
                    f"成功获取办事进度", len(applications)
                )
                return applications
            else:
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取办事进度", "失败", "未获取到办事进度"
                )
                return None
                
        except Exception as e:
            self.logger.error(f"爬取办事进度失败: {e}")
            self.db_manager.save_crawl_log(
                self.current_user, "爬取办事进度", "错误", f"爬取过程出错: {str(e)}"
            )
            return None
    
    def crawl_certificates(self):
        """爬取证书信息"""
        try:
            if not self.current_user:
                self.logger.error("用户未登录")
                return None
            
            self.logger.info("开始爬取证书信息...")
            certificates = self.data_crawler.get_certificates()
            
            if certificates is not None:
                # 保存到数据库
                self.db_manager.save_certificates(self.current_user, certificates)
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取证书信息", "成功", 
                    f"成功获取证书信息", len(certificates)
                )
                return certificates
            else:
                self.db_manager.save_crawl_log(
                    self.current_user, "爬取证书信息", "失败", "未获取到证书信息"
                )
                return None
                
        except Exception as e:
            self.logger.error(f"爬取证书信息失败: {e}")
            self.db_manager.save_crawl_log(
                self.current_user, "爬取证书信息", "错误", f"爬取过程出错: {str(e)}"
            )
            return None
    
    def crawl_all_data(self):
        """爬取所有数据"""
        try:
            if not self.current_user:
                self.logger.error("用户未登录")
                return None
            
            self.logger.info("开始爬取所有数据...")
            
            all_data = {
                'personal_info': self.crawl_personal_info(),
                'applications': self.crawl_applications(),
                'certificates': self.crawl_certificates(),
                'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 统计成功获取的数据项
            success_count = sum(1 for v in all_data.values() if v is not None and v != [])
            
            self.db_manager.save_crawl_log(
                self.current_user, "爬取所有数据", "完成", 
                f"数据爬取完成，成功获取 {success_count} 类数据", success_count
            )
            
            return all_data
            
        except Exception as e:
            self.logger.error(f"爬取所有数据失败: {e}")
            self.db_manager.save_crawl_log(
                self.current_user, "爬取所有数据", "错误", f"爬取过程出错: {str(e)}"
            )
            return None
    
    def get_cached_data(self, username):
        """从数据库获取缓存的数据"""
        try:
            cached_data = {
                'personal_info': self.db_manager.get_user_info(username),
                'applications': self.db_manager.get_applications(username),
                'certificates': self.db_manager.get_certificates(username)
            }
            return cached_data
        except Exception as e:
            self.logger.error(f"获取缓存数据失败: {e}")
            return None
    
    def export_user_data(self, username, format='excel'):
        """导出用户数据"""
        try:
            if format.lower() == 'excel':
                return self.db_manager.export_to_excel(username)
            elif format.lower() == 'json':
                return self.data_crawler.export_data_to_json()
            else:
                self.logger.error(f"不支持的导出格式: {format}")
                return None
        except Exception as e:
            self.logger.error(f"导出用户数据失败: {e}")
            return None
    
    def get_crawl_history(self, username):
        """获取爬取历史"""
        try:
            return self.db_manager.get_crawl_logs(username)
        except Exception as e:
            self.logger.error(f"获取爬取历史失败: {e}")
            return []
