"""
苏服办爬虫系统测试
"""

import unittest
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crawler.sufuban_crawler import SufubanAppCrawler
from database.models import DatabaseManager


class TestSufubanCrawler(unittest.TestCase):
    """苏服办爬虫测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.crawler = SufubanAppCrawler(headless=True)
        self.db_manager = DatabaseManager()
    
    def tearDown(self):
        """测试后清理"""
        if self.crawler:
            self.crawler.stop()
    
    def test_crawler_initialization(self):
        """测试爬虫初始化"""
        self.assertIsNotNone(self.crawler)
        self.assertIsNotNone(self.crawler.login_manager)
        self.assertIsNotNone(self.crawler.db_manager)
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        self.assertIsNotNone(self.db_manager)
        
        # 测试数据库文件是否创建
        db_path = Path(self.db_manager.db_path)
        self.assertTrue(db_path.exists())
    
    def test_crawler_start_stop(self):
        """测试爬虫启动和停止"""
        # 测试启动
        result = self.crawler.start()
        self.assertTrue(result)
        
        # 测试停止
        self.crawler.stop()
    
    def test_data_cache(self):
        """测试数据缓存功能"""
        test_username = "test_user"
        
        # 测试保存用户信息
        test_data = {
            'name': '测试用户',
            'phone': '13800138000',
            'email': '<EMAIL>'
        }
        
        result = self.db_manager.save_user_info(test_username, test_data)
        self.assertTrue(result)
        
        # 测试获取用户信息
        cached_data = self.db_manager.get_user_info(test_username)
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data['name'], '测试用户')
    
    def test_export_functionality(self):
        """测试导出功能"""
        test_username = "test_user"
        
        # 先保存一些测试数据
        test_data = {
            'name': '测试用户',
            'phone': '13800138000'
        }
        self.db_manager.save_user_info(test_username, test_data)
        
        # 测试导出
        export_file = self.db_manager.export_to_excel(test_username)
        if export_file:
            self.assertTrue(Path(export_file).exists())


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.db_manager = DatabaseManager()
    
    def test_save_and_get_user_info(self):
        """测试保存和获取用户信息"""
        test_username = "test_user_2"
        test_data = {
            'name': '测试用户2',
            'id_card': '320000199001010000',
            'phone': '13900139000',
            'email': '<EMAIL>',
            'address': '江苏省苏州市'
        }
        
        # 保存数据
        result = self.db_manager.save_user_info(test_username, test_data)
        self.assertTrue(result)
        
        # 获取数据
        retrieved_data = self.db_manager.get_user_info(test_username)
        self.assertIsNotNone(retrieved_data)
        self.assertEqual(retrieved_data['name'], '测试用户2')
        self.assertEqual(retrieved_data['phone'], '13900139000')
    
    def test_save_applications(self):
        """测试保存办事进度"""
        test_username = "test_user_3"
        test_applications = [
            {
                '序号': 'APP001',
                '事项名称': '测试事项1',
                '状态': '已受理',
                '申请时间': '2025-09-01',
                '更新时间': '2025-09-02'
            },
            {
                '序号': 'APP002',
                '事项名称': '测试事项2',
                '状态': '审核中',
                '申请时间': '2025-09-02',
                '更新时间': '2025-09-02'
            }
        ]
        
        # 保存数据
        result = self.db_manager.save_applications(test_username, test_applications)
        self.assertTrue(result)
        
        # 获取数据
        retrieved_apps = self.db_manager.get_applications(test_username)
        self.assertEqual(len(retrieved_apps), 2)
    
    def test_crawl_log(self):
        """测试爬取日志"""
        test_username = "test_user_4"
        
        # 保存日志
        self.db_manager.save_crawl_log(
            test_username, "测试操作", "成功", "测试消息", 5
        )
        
        # 获取日志
        logs = self.db_manager.get_crawl_logs(test_username, 10)
        self.assertGreater(len(logs), 0)
        self.assertEqual(logs[0]['action'], "测试操作")


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(unittest.makeSuite(TestSufubanCrawler))
    suite.addTest(unittest.makeSuite(TestDatabaseManager))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n所有测试通过！")
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
